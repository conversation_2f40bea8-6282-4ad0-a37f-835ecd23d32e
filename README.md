# RTB MS Exporter

RTB微服务数据导出工具，用于从数据库导出RTB相关数据并上传到对象存储服务。

## 功能特性

- **数据导出**: 支持从MySQL数据库导出Budget统计、设备映射、资源目标等数据
- **定时任务**: 基于Cron表达式的灵活任务调度
- **文件监听**: 实时监听AdIndex文件变化并自动上传
- **多云存储**: 支持百度云BOS和腾讯云COS对象存储
- **本地备份**: 支持本地文件备份和自动清理
- **调试模式**: 支持OSS调试模式，只打印上传操作而不实际上传
- **日志管理**: 基于Zap的结构化日志，支持日志轮转

## 项目结构

```
├── cmd/                    # 应用入口
│   └── main.go
├── conf/                   # 配置文件
│   ├── conf.go
│   └── rtb_ms_exporter.yaml
├── internal/               # 内部模块
│   ├── models/            # 数据模型和数据库操作
│   ├── scheduler/         # 任务调度器
│   ├── storage/           # 对象存储管理
│   ├── watcher/           # 文件监听器
│   └── zaplog/            # 日志配置
├── Makefile               # 构建脚本
├── go.mod                 # Go模块依赖
└── README.md              # 项目说明
```

## 快速开始

### 环境要求

- Go 1.21.3+
- MySQL 数据库
- 百度云BOS或腾讯云COS账号（可选）

### 安装依赖

```bash
go mod download
```

### 配置文件

复制并修改配置文件：

```bash
cp conf/rtb_ms_exporter.yaml conf/rtb_ms_exporter.yaml.local
```

主要配置项：

- **数据库配置**: 配置RTB相关数据库连接信息
- **对象存储配置**: 配置BOS/COS访问密钥和存储桶
- **定时任务配置**: 配置各类任务的执行间隔
- **文件监听配置**: 配置AdIndex文件监听目录和规则
- **本地存储配置**: 配置本地备份目录

### 构建和运行

```bash
# 构建
make build

# 运行
make run

# 或者直接运行
./rtb_ms_exporter -c conf/rtb_ms_exporter.yaml
```

### 命令行参数

```bash
./rtb_ms_exporter [选项]

选项:
  -c string
        配置文件路径 (默认: "conf/rtb_ms_exporter.yaml")
  -v    显示版本信息
  -debug-oss
        启用OSS调试模式（只打印上传操作，不实际上传）
  -update-on-startup
        启动时立即执行一次所有任务
```

## 部署位置
https://atlantis.bluemedia-inc.com/#/vmapp/detail/baseinfo?svc_id=2887&namespace_id=1000

## 核心模块

### 数据模型 (models)

- **BudgetStats**: Budget统计数据结构
- **DeviceMapping**: 设备映射数据结构  
- **ResourceTarget**: 资源目标数据结构
- **DatabaseManager**: 数据库连接和查询管理

### 任务调度器 (scheduler)

- 支持基于Cron表达式的定时任务
- 支持Budget、Device、Resource三类数据导出任务
- 支持启动时立即执行任务
- 支持本地和远程文件清理

### 存储管理 (storage)

- **StorageProvider**: 对象存储提供商接口
- **COSProvider**: 腾讯云COS存储实现
- **BOSProvider**: 百度云BOS存储实现
- **StorageManager**: 多存储提供商管理

### 文件监听器 (watcher)

- **AdIndexWatcher**: AdIndex文件监听和上传
- **LocalFileManager**: 本地文件管理和备份
- 支持文件完整性检查和版本管理

## 开发指南

### 代码格式化

```bash
make fmt
```

### 代码检查

```bash
make vet
```

### 运行测试

```bash
make test
```

### 清理构建文件

```bash
make clean
```

## 依赖库

- **github.com/robfig/cron/v3**: Cron任务调度
- **go.uber.org/zap**: 结构化日志
- **github.com/go-sql-driver/mysql**: MySQL驱动
- **github.com/fsnotify/fsnotify**: 文件系统监听
- **github.com/baidubce/bce-sdk-go**: 百度云SDK
- **github.com/tencentyun/cos-go-sdk-v5**: 腾讯云COS SDK
- **github.com/spf13/viper**: 配置管理
- **github.com/natefinch/lumberjack**: 日志轮转

## 许可证

本项目采用内部许可证，仅供内部使用。

## 功能特性

### 数据源支持

1. **AdIndex文件监听**
   - 监听指定目录下的ad_*.data.xxxxx文件
   - 自动上传到对象存储的adindex目录
   - 当所有类型文件完成后自动创建VERSION文件
   - 支持的文件类型：ad_campaign, ad_creative, ad_poi, ad_promotion, ad_sponsor, ad_strategy, ad_summary, ad_tracking

2. **Budget数据导出**
   - 从RTB数据库查询统计数据
   - 支持可配置的定时执行（默认10秒）
   - 输出TSV格式文件到budget/stats.data

3. **Device映射导出**
   - 从Device数据库查询设备映射数据
   - 支持可配置的定时执行（默认24小时）
   - 输出TSV格式文件到device/device_mapping.txt

4. **Resource目标导出**
   - 从Bid数据库查询资源目标数据
   - 支持可配置的定时执行（默认10分钟）
   - 输出TSV格式文件到resource/resource-target.data

### 对象存储支持

- **百度云BOS**：支持百度云对象存储
- **腾讯云COS**：支持腾讯云对象存储
- **可扩展性**：易于添加其他对象存储提供商
- **多提供商同步**：支持同时上传到多个对象存储

### 文件管理

- **本地缓存**：除adindex外的文件都会在本地保存一份
- **自动清理**：支持配置本地和远程文件的清理策略
- **版本管理**：AdIndex文件支持版本号管理

## 项目结构

```
rtb_ms_exporter_go/
├── cmd/
│   └── main.go              # 主程序入口
├── conf/
│   ├── conf.go              # 配置结构定义
│   ├── rtb_ms_exporter.yaml # 示例配置文件
│   └── rtb_model_server.yaml # 原有配置文件
├── internal/
│   ├── models/
│   │   └── models.go        # 数据模型和数据库操作
│   ├── scheduler/
│   │   └── scheduler.go     # 定时任务调度器
│   ├── storage/
│   │   └── storage.go       # 对象存储抽象层
│   ├── watcher/
│   │   └── watcher.go       # 文件监听和本地文件管理
│   └── zaplog/
│       └── logger.go        # 日志组件
├── go.mod
├── go.sum
└── README.md
```

## 配置说明

### 数据库配置

```yaml
databases:
  rtb:      # RTB数据库，用于budget数据
    host: localhost
    port: 3306
    user: rtb_user
    password: rtb_password
    dbname: rtb
    charset: utf8mb4
  device:   # Device数据库，用于设备映射
    host: localhost
    port: 3306
    user: device_user
    password: device_password
    dbname: device
    charset: utf8mb4
  bid:      # Bid数据库，用于资源目标
    host: localhost
    port: 3306
    user: bid_user
    password: bid_password
    dbname: bid
    charset: utf8mb4
```

### 对象存储配置

```yaml
oss_providers:
  bos:      # 百度云BOS
    type: bos
    endpoint: https://bj.bcebos.com
    access_key_id: your_bos_access_key
    access_key_secret: your_bos_secret_key
    bucket_name: your-bos-bucket
    region: bj
    cleanup_days: 1
  cos:      # 腾讯云COS
    type: cos
    endpoint: https://your-bucket-**********.cos.ap-beijing.myqcloud.com
    access_key_id: your_cos_secret_id
    access_key_secret: your_cos_secret_key
    bucket_name: your-cos-bucket
    region: ap-beijing
    cleanup_days: 1
```

### 定时任务配置

```yaml
schedule:
  budget_interval: "10s"    # budget任务间隔
  device_interval: "24h"    # device任务间隔
  resource_interval: "10m"  # resource任务间隔
```

支持的时间格式：
- 秒："30s"
- 分钟："5m"
- 小时："2h"
- 天："24h"

## 使用方法

### 编译

```bash
go build -o rtb_ms_exporter cmd/main.go
```

### 运行

```bash
# 使用默认配置文件
./rtb_ms_exporter

# 指定配置文件
./rtb_ms_exporter -c /path/to/config.yaml

# 查看版本
./rtb_ms_exporter -v
```

### 输出文件结构

对象存储中的文件结构：

```
├── adindex/
│   ├── ad_campaign.data.xxxxx
│   ├── ad_creative.data.xxxxx
│   ├── ad_poi.data.xxxxx
│   ├── ad_promotion.data.xxxxx
│   ├── ad_sponsor.data.xxxxx
│   ├── ad_strategy.data.xxxxx
│   ├── ad_summary.data.xxxxx
│   ├── ad_tracking.data.xxxxx
│   └── VERSION
├── budget/
│   └── stats.data
├── device/
│   └── device_mapping.txt
└── resource/
    └── resource-target.data
```

## 依赖包

- **github.com/fsnotify/fsnotify**: 文件系统监听
- **github.com/go-sql-driver/mysql**: MySQL数据库驱动
- **github.com/robfig/cron/v3**: 定时任务调度
- **github.com/spf13/viper**: 配置文件解析
- **github.com/baidubce/bce-sdk-go**: 百度云SDK
- **github.com/tencentyun/cos-go-sdk-v5**: 腾讯云SDK
- **go.uber.org/zap**: 结构化日志

## 扩展性

### 添加新的对象存储提供商

1. 在`internal/storage/storage.go`中实现`StorageProvider`接口
2. 在`NewStorageManager.InitProviders`中添加新的case分支
3. 更新配置文件支持新的存储类型

### 添加新的数据源

1. 在`internal/models/models.go`中添加新的查询方法
2. 在`internal/scheduler/scheduler.go`中添加新的任务类型
3. 更新配置文件添加新的调度配置

## 监控和日志

- 使用结构化日志记录所有操作
- 支持日志轮转和压缩
- 记录文件上传、数据库查询、任务执行等关键操作
- 错误处理和重试机制

## 注意事项

1. **文件完整性**：AdIndex文件监听器会等待文件写入完成后再处理
2. **并发安全**：所有组件都是并发安全的
3. **资源清理**：程序退出时会正确关闭所有资源
4. **错误恢复**：定时任务失败不会影响其他任务的执行
5. **配置验证**：启动时会验证所有配置的有效性