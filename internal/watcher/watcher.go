package watcher

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"rtb_ms_exporter_go/internal/storage"
	"sort"
	"strings"
	"time"

	"github.com/fsnotify/fsnotify"
	"go.uber.org/zap"
)

// FileInfo 文件信息结构
type FileInfo struct {
	FileName string
	FilePath string
	Version  string
	ModTime  time.Time
}

// AdIndexWatcher AdIndex文件监听器
type AdIndexWatcher struct {
	watcher        *fsnotify.Watcher
	watchDir       string
	filePattern    *regexp.Regexp
	storageManager *storage.StorageManager
	logger         *zap.Logger
	fileTracker    map[string]map[string]bool // 按版本跟踪已完成的文件类型 [version][fileType]bool
	requiredTypes  []string                   // 需要的文件类型
	latestFiles    map[string]FileInfo        // 跟踪每种类型的最新文件
	keepFiles      int                        // 每种类型保留的文件数量
	currentVersion string                     // 当前处理的版本
}

// NewAdIndexWatcher 创建AdIndex监听器
func NewAdIndexWatcher(watchDir, pattern string, keepFiles int, storageManager *storage.StorageManager, logger *zap.Logger) (*AdIndexWatcher, error) {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return nil, err
	}
	
	filePattern, err := regexp.Compile(pattern)
	if err != nil {
		return nil, fmt.Errorf("invalid file pattern: %v", err)
	}
	
	// 定义需要的文件类型
	requiredTypes := []string{
		"ad_campaign", "ad_creative", "ad_poi", "ad_promotion",
		"ad_sponsor", "ad_strategy", "ad_summary", "ad_tracking",
	}
	
	// 设置默认保留文件数量
	if keepFiles <= 0 {
		keepFiles = 3
	}
	
	return &AdIndexWatcher{
		watcher:        watcher,
		watchDir:       watchDir,
		filePattern:    filePattern,
		storageManager: storageManager,
		logger:         logger,
		fileTracker:    make(map[string]map[string]bool),
		requiredTypes:  requiredTypes,
		latestFiles:    make(map[string]FileInfo),
		keepFiles:      keepFiles,
	}, nil
}

// Start 开始监听
func (w *AdIndexWatcher) Start() error {
	// 添加监听目录
	err := w.watcher.Add(w.watchDir)
	if err != nil {
		return err
	}
	
	w.logger.Info("AdIndex watcher started", zap.String("dir", w.watchDir))
	
	// 启动事件处理协程
	go w.handleEvents()
	
	// 扫描已存在的文件
	go w.scanExistingFiles()
	
	return nil
}

// scanExistingFiles 扫描已存在的文件
func (w *AdIndexWatcher) scanExistingFiles() {
	// 延迟一秒，确保监听器已经启动
	time.Sleep(1 * time.Second)
	
	files, err := filepath.Glob(filepath.Join(w.watchDir, "*"))
	if err != nil {
		w.logger.Error("Failed to scan existing files", zap.Error(err))
		return
	}
	
	// 按文件类型分组，找出每种类型的最新文件
	typeLatestFiles := make(map[string]FileInfo)
	
	for _, filePath := range files {
		fileName := filepath.Base(filePath)
		
		// 检查文件是否匹配模式
		if !w.filePattern.MatchString(fileName) {
			continue
		}
		
		fileType := w.extractFileType(fileName)
		version := w.extractVersion(fileName)
		if fileType == "" || version == "" {
			continue
		}
		
		stat, err := os.Stat(filePath)
		if err != nil {
			continue
		}
		
		currentFile := FileInfo{
			FileName: fileName,
			FilePath: filePath,
			Version:  version,
			ModTime:  stat.ModTime(),
		}
		
		// 检查是否是该类型的最新文件
		if existingFile, exists := typeLatestFiles[fileType]; !exists || version > existingFile.Version {
			typeLatestFiles[fileType] = currentFile
		}
	}
	
	// 处理每种类型的最新文件
	for fileType, fileInfo := range typeLatestFiles {
		w.logger.Info("Found latest existing file", 
			zap.String("type", fileType),
			zap.String("file", fileInfo.FileName),
			zap.String("version", fileInfo.Version))
		w.handleFileWrite(fileInfo.FilePath)
	}
}

// handleEvents 处理文件系统事件
func (w *AdIndexWatcher) handleEvents() {
	for {
		select {
		case event, ok := <-w.watcher.Events:
			if !ok {
				return
			}
			
			// 处理文件创建和写入事件
			if event.Op&fsnotify.Create == fsnotify.Create || event.Op&fsnotify.Write == fsnotify.Write {
				w.handleFileWrite(event.Name)
			}
			
		case err, ok := <-w.watcher.Errors:
			if !ok {
				return
			}
			w.logger.Error("Watcher error", zap.Error(err))
		}
	}
}

// handleFileWrite 处理文件写入事件
func (w *AdIndexWatcher) handleFileWrite(filePath string) {
	fileName := filepath.Base(filePath)
	
	// 检查文件是否匹配模式
	if !w.filePattern.MatchString(fileName) {
		return
	}
	
	w.logger.Info("Detected file change", zap.String("file", fileName))
	
	// 等待文件写入完成
	time.Sleep(1 * time.Second)
	
	// 检查文件是否存在且可读
	if !w.isFileReady(filePath) {
		w.logger.Warn("File not ready", zap.String("file", filePath))
		return
	}
	
	// 提取文件类型和版本
	fileType := w.extractFileType(fileName)
	if fileType == "" {
		w.logger.Warn("Cannot extract file type", zap.String("file", fileName))
		return
	}
	
	version := w.extractVersion(fileName)
	if version == "" {
		w.logger.Warn("Cannot extract version", zap.String("file", fileName))
		return
	}
	
	// 获取文件修改时间
	stat, err := os.Stat(filePath)
	if err != nil {
		w.logger.Error("Failed to get file stat", zap.String("file", filePath), zap.Error(err))
		return
	}
	
	// 检查是否需要更新（比较版本号，取最新的）
	currentFile := FileInfo{
		FileName: fileName,
		FilePath: filePath,
		Version:  version,
		ModTime:  stat.ModTime(),
	}
	
	if existingFile, exists := w.latestFiles[fileType]; exists {
		// 比较版本号，只有新版本才上传
		if version <= existingFile.Version {
			w.logger.Info("File version is not newer, skipping upload", 
				zap.String("file", fileName),
				zap.String("current_version", version),
				zap.String("existing_version", existingFile.Version))
			return
		}
		w.logger.Info("Found newer version, will upload", 
			zap.String("file", fileName),
			zap.String("new_version", version),
			zap.String("old_version", existingFile.Version))
	} else {
		w.logger.Info("First file of this type, will upload", 
			zap.String("file", fileName),
			zap.String("type", fileType))
	}
	

	
	// 初始化版本跟踪器（如果不存在）
	if w.fileTracker[version] == nil {
		w.fileTracker[version] = make(map[string]bool)
	}
	
	// 检查该文件类型是否已经完成过（防止重复处理）
	if w.fileTracker[version][fileType] {
		w.logger.Info("File type already completed in this version, skipping", 
			zap.String("file", fileName),
			zap.String("type", fileType),
			zap.String("version", version))
		return
	}
	
	// 上传文件到对象存储
	if err := w.uploadFile(filePath); err != nil {
		w.logger.Error("Failed to upload file", zap.String("file", filePath), zap.Error(err))
		return
	}
	
	// 更新最新文件记录和跟踪状态
	w.latestFiles[fileType] = currentFile
	w.fileTracker[version][fileType] = true
	w.currentVersion = version
	w.logger.Info("File uploaded and tracked", zap.String("type", fileType), zap.String("file", fileName), zap.String("version", version))
	
	// 每次文件上传后都执行清理
	w.cleanupOldFiles()
	
	// 检查是否所有文件都已完成
	if w.allFilesCompleted() {
		w.createVersionFile(fileName)
	}
}

// isFileReady 检查文件是否准备就绪
func (w *AdIndexWatcher) isFileReady(filePath string) bool {
	stat, err := os.Stat(filePath)
	if err != nil {
		return false
	}
	
	// 检查文件大小是否稳定（简单的完整性检查）
	initialSize := stat.Size()
	time.Sleep(500 * time.Millisecond)
	
	stat, err = os.Stat(filePath)
	if err != nil {
		return false
	}
	
	return stat.Size() == initialSize && stat.Size() > 0
}

// uploadFile 上传文件到对象存储
func (w *AdIndexWatcher) uploadFile(filePath string) error {
	startTime := time.Now()
	
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()
	
	fileName := filepath.Base(filePath)
	key := fmt.Sprintf("adindex/%s", fileName)
	
	// 获取文件大小
	stat, _ := file.Stat()
	fileSize := stat.Size()
	
	w.logger.Info("Starting file upload", 
		zap.String("file", fileName),
		zap.Int64("size", fileSize))
	
	err = w.storageManager.UploadToAll(key, file, "application/octet-stream")
	
	duration := time.Since(startTime)
	if err != nil {
		w.logger.Error("File upload failed", 
			zap.String("file", fileName),
			zap.Duration("duration", duration),
			zap.Error(err))
	} else {
		w.logger.Info("File upload completed", 
			zap.String("file", fileName),
			zap.Int64("size", fileSize),
			zap.Duration("duration", duration))
	}
	
	return err
}

// extractFileType 从文件名提取文件类型
func (w *AdIndexWatcher) extractFileType(fileName string) string {
	// 匹配 ad_TYPE.data.yyyymmdd_hhiiss 格式
	pattern := regexp.MustCompile(`^(ad_\w+)\.data\.\d{8}_\d{6}$`)
	matches := pattern.FindStringSubmatch(fileName)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// allFilesCompleted 检查当前版本是否所有必需的文件都已完成
func (w *AdIndexWatcher) allFilesCompleted() bool {
	if w.currentVersion == "" {
		return false
	}
	
	versionTracker := w.fileTracker[w.currentVersion]
	if versionTracker == nil {
		return false
	}
	
	completedCount := 0
	var completedTypes []string
	var missingTypes []string
	
	for _, fileType := range w.requiredTypes {
		if versionTracker[fileType] {
			completedCount++
			completedTypes = append(completedTypes, fileType)
		} else {
			missingTypes = append(missingTypes, fileType)
		}
	}
	
	w.logger.Info("File completion status", 
		zap.String("version", w.currentVersion),
		zap.Int("completed_count", completedCount),
		zap.Int("required_count", len(w.requiredTypes)),
		zap.Strings("completed_types", completedTypes),
		zap.Strings("missing_types", missingTypes))
	
	allCompleted := completedCount == len(w.requiredTypes)
	w.logger.Info("All files completed check", 
		zap.String("version", w.currentVersion),
		zap.Bool("all_completed", allCompleted))
	
	return allCompleted
}

// createVersionFile 创建VERSION文件
func (w *AdIndexWatcher) createVersionFile(lastFileName string) {
	w.logger.Info("All files completed, creating VERSION file")
	
	// 从最后一个文件名提取版本号
	version := w.extractVersion(lastFileName)
	if version == "" {
		w.logger.Error("Failed to extract version from filename", zap.String("file", lastFileName))
		return
	}
	
	// 创建VERSION文件内容
	versionContent := strings.NewReader(version)
	key := "adindex/VERSION"
	
	// 记录上传开始时间
	startTime := time.Now()
	w.logger.Info("Starting VERSION file upload", zap.String("version", version))
	
	// 上传VERSION文件
	if err := w.storageManager.UploadToAll(key, versionContent, "text/plain"); err != nil {
		duration := time.Since(startTime)
		w.logger.Error("Failed to upload VERSION file", 
			zap.String("version", version),
			zap.Duration("duration", duration),
			zap.Error(err))
		return
	}
	
	duration := time.Since(startTime)
	w.logger.Info("VERSION file created", 
		zap.String("version", version),
		zap.Duration("duration", duration))
	
	// 清理当前版本的跟踪记录和最新文件记录，准备下一轮
	delete(w.fileTracker, version)
	// 清理latestFiles，避免后续文件事件误判版本
	w.latestFiles = make(map[string]FileInfo)
	w.currentVersion = ""
}

// extractVersion 从文件名提取版本号
func (w *AdIndexWatcher) extractVersion(fileName string) string {
	// 匹配 ad_campaign.data.yyyymmdd_hhiiss 格式，提取 yyyymmdd_hhiiss
	pattern := regexp.MustCompile(`\.data\.(\d{8}_\d{6})$`)
	matches := pattern.FindStringSubmatch(fileName)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// Stop 停止监听
func (w *AdIndexWatcher) Stop() error {
	w.logger.Info("Stopping AdIndex watcher")
	return w.watcher.Close()
}

// cleanupOldFiles 清理旧文件，每种类型只保留最近的keepFiles个
func (w *AdIndexWatcher) cleanupOldFiles() {
	w.logger.Info("Starting cleanup of old adindex files", zap.Int("keep_files", w.keepFiles))
	
	// 清理本地文件
	w.cleanupLocalFiles()
	
	// 清理远程文件
	w.cleanupRemoteFiles()
}

// cleanupLocalFiles 清理本地旧文件
func (w *AdIndexWatcher) cleanupLocalFiles() {
	files, err := filepath.Glob(filepath.Join(w.watchDir, "*"))
	if err != nil {
		w.logger.Error("Failed to scan local files for cleanup", zap.Error(err))
		return
	}
	
	// 按文件类型分组
	typeFiles := make(map[string][]FileInfo)
	
	for _, filePath := range files {
		fileName := filepath.Base(filePath)
		
		// 检查文件是否匹配模式
		if !w.filePattern.MatchString(fileName) {
			continue
		}
		
		fileType := w.extractFileType(fileName)
		version := w.extractVersion(fileName)
		if fileType == "" || version == "" {
			continue
		}
		
		stat, err := os.Stat(filePath)
		if err != nil {
			continue
		}
		
		fileInfo := FileInfo{
			FileName: fileName,
			FilePath: filePath,
			Version:  version,
			ModTime:  stat.ModTime(),
		}
		
		typeFiles[fileType] = append(typeFiles[fileType], fileInfo)
	}
	
	// 对每种类型的文件进行清理
	for fileType, files := range typeFiles {
		if len(files) <= w.keepFiles {
			continue
		}
		
		// 按版本号排序（降序，最新的在前面）
		sort.Slice(files, func(i, j int) bool {
			return files[i].Version > files[j].Version
		})
		
		// 删除多余的文件
		for i := w.keepFiles; i < len(files); i++ {
			file := files[i]
			if err := os.Remove(file.FilePath); err != nil {
				w.logger.Error("Failed to remove old local file", 
					zap.String("file", file.FileName),
					zap.String("type", fileType),
					zap.Error(err))
			} else {
				w.logger.Info("Removed old local file", 
					zap.String("file", file.FileName),
					zap.String("type", fileType),
					zap.String("version", file.Version))
			}
		}
	}
}

// cleanupRemoteFiles 清理远程旧文件
func (w *AdIndexWatcher) cleanupRemoteFiles() {
	// 获取所有存储提供商
	providers := w.storageManager.GetAllProviders()
	for providerName, provider := range providers {
		
		// 列出adindex目录下的所有文件
		files, err := provider.ListFiles("adindex/")
		if err != nil {
			w.logger.Error("Failed to list remote files", 
				zap.String("provider", providerName), zap.Error(err))
			continue
		}
		
		// 按文件类型分组
		typeFiles := make(map[string][]storage.FileInfo)
		
		for _, file := range files {
			// 跳过VERSION文件
			if strings.HasSuffix(file.Key, "/VERSION") {
				continue
			}
			
			fileName := filepath.Base(file.Key)
			
			// 检查文件是否匹配模式
			if !w.filePattern.MatchString(fileName) {
				continue
			}
			
			fileType := w.extractFileType(fileName)
			if fileType == "" {
				continue
			}
			
			typeFiles[fileType] = append(typeFiles[fileType], file)
		}
		
		// 对每种类型的文件进行清理
		for fileType, files := range typeFiles {
			if len(files) <= w.keepFiles {
				continue
			}
			
			// 按最后修改时间排序（降序，最新的在前面）
			sort.Slice(files, func(i, j int) bool {
				return files[i].LastModified.After(files[j].LastModified)
			})
			
			// 删除多余的文件
			for i := w.keepFiles; i < len(files); i++ {
				file := files[i]
				if err := provider.DeleteFile(file.Key); err != nil {
					w.logger.Error("Failed to remove old remote file", 
						zap.String("provider", providerName),
						zap.String("file", file.Key),
						zap.String("type", fileType),
						zap.Error(err))
				} else {
					w.logger.Info("Removed old remote file", 
						zap.String("provider", providerName),
						zap.String("file", file.Key),
						zap.String("type", fileType))
				}
			}
		}
	}
}

// LocalFileManager 本地文件管理器
type LocalFileManager struct {
	baseDir string
	logger  *zap.Logger
}

// NewLocalFileManager 创建本地文件管理器
func NewLocalFileManager(baseDir string, logger *zap.Logger) *LocalFileManager {
	return &LocalFileManager{
		baseDir: baseDir,
		logger:  logger,
	}
}

// SaveFile 保存文件到本地
func (lm *LocalFileManager) SaveFile(relativePath string, content io.Reader) error {
	filePath := filepath.Join(lm.baseDir, relativePath)
	
	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}
	
	// 创建文件
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()
	
	// 写入内容
	_, err = io.Copy(file, content)
	if err != nil {
		return err
	}
	
	lm.logger.Info("File saved locally", zap.String("path", filePath))
	return nil
}

// CleanupOldFiles 清理本地旧文件
func (lm *LocalFileManager) CleanupOldFiles(days int) error {
	cutoff := time.Now().AddDate(0, 0, -days)
	
	return filepath.Walk(lm.baseDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// 跳过目录
		if info.IsDir() {
			return nil
		}
		
		// 检查文件修改时间
		if info.ModTime().Before(cutoff) {
			if err := os.Remove(path); err != nil {
				lm.logger.Error("Failed to remove old file", zap.String("path", path), zap.Error(err))
				return err
			}
			lm.logger.Info("Removed old file", zap.String("path", path))
		}
		
		return nil
	})
}

// BaseDir 获取基础目录
func (lm *LocalFileManager) BaseDir() string {
	return lm.baseDir
}