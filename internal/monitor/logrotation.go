package monitor

import (
	"os"
	"path/filepath"
	"strings"
	"sync"
	"syscall"
	"time"

	"go.uber.org/zap"
)

// LogRotationMonitor 日志轮转监控器
type LogRotationMonitor struct {
	logger  *zap.Logger
	logPath string
	mu      sync.RWMutex
	data    LogRotationData
	stopCh  chan struct{}
}

// LogRotationData 日志轮转监控数据
type LogRotationData struct {
	// 当前日志文件信息
	CurrentLogPath    string    `json:"current_log_path"`
	CurrentLogSizeMB  int64     `json:"current_log_size_mb"`
	CurrentLogModTime time.Time `json:"current_log_mod_time"`

	// 备份文件信息
	BackupCount       int           `json:"backup_count"`
	BackupFiles       []LogFileInfo `json:"backup_files"`
	TotalBackupSizeMB int64         `json:"total_backup_size_mb"`

	// 磁盘空间信息
	DiskTotalMB      int64   `json:"disk_total_mb"`
	DiskUsedMB       int64   `json:"disk_used_mb"`
	DiskAvailableMB  int64   `json:"disk_available_mb"`
	DiskUsagePercent float64 `json:"disk_usage_percent"`

	// 轮转状态
	LastRotationTime  time.Time `json:"last_rotation_time"`
	RotationCount     int64     `json:"rotation_count"`
	IsRotationHealthy bool      `json:"is_rotation_healthy"`

	// 更新时间
	LastUpdate time.Time `json:"last_update"`
}

// LogFileInfo 日志文件信息
type LogFileInfo struct {
	FileName     string    `json:"file_name"`
	SizeMB       int64     `json:"size_mb"`
	ModTime      time.Time `json:"mod_time"`
	IsCompressed bool      `json:"is_compressed"`
}

// NewLogRotationMonitor 创建日志轮转监控器
func NewLogRotationMonitor(logPath string, logger *zap.Logger) *LogRotationMonitor {
	return &LogRotationMonitor{
		logger:  logger,
		logPath: logPath,
		stopCh:  make(chan struct{}),
	}
}

// Start 启动日志轮转监控
func (lrm *LogRotationMonitor) Start() error {
	lrm.logger.Info("Starting log rotation monitor", zap.String("log_path", lrm.logPath))

	// 立即收集一次数据
	lrm.collectLogRotationData()

	// 启动定期收集
	go lrm.collectLoop()

	return nil
}

// Stop 停止日志轮转监控
func (lrm *LogRotationMonitor) Stop() {
	close(lrm.stopCh)
	lrm.logger.Info("Log rotation monitor stopped")
}

// GetLogRotationData 获取日志轮转数据
func (lrm *LogRotationMonitor) GetLogRotationData() LogRotationData {
	lrm.mu.RLock()
	defer lrm.mu.RUnlock()
	return lrm.data
}

// collectLoop 数据收集循环
func (lrm *LogRotationMonitor) collectLoop() {
	ticker := time.NewTicker(1 * time.Minute) // 每分钟收集一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			lrm.collectLogRotationData()
		case <-lrm.stopCh:
			return
		}
	}
}

// collectLogRotationData 收集日志轮转数据
func (lrm *LogRotationMonitor) collectLogRotationData() {
	lrm.mu.Lock()
	defer lrm.mu.Unlock()

	// 获取当前日志文件信息
	currentLogInfo := lrm.getCurrentLogInfo()

	// 获取备份文件信息
	backupFiles := lrm.getBackupFiles()

	// 获取磁盘空间信息
	diskInfo := lrm.getDiskInfo()

	// 检查轮转健康状态
	isHealthy := lrm.checkRotationHealth(currentLogInfo, backupFiles)

	// 计算总备份大小
	var totalBackupSize int64
	for _, file := range backupFiles {
		totalBackupSize += file.SizeMB
	}

	lrm.data = LogRotationData{
		CurrentLogPath:    lrm.logPath,
		CurrentLogSizeMB:  currentLogInfo.SizeMB,
		CurrentLogModTime: currentLogInfo.ModTime,
		BackupCount:       len(backupFiles),
		BackupFiles:       backupFiles,
		TotalBackupSizeMB: totalBackupSize,
		DiskTotalMB:       diskInfo.TotalMB,
		DiskUsedMB:        diskInfo.UsedMB,
		DiskAvailableMB:   diskInfo.AvailableMB,
		DiskUsagePercent:  diskInfo.UsagePercent,
		LastRotationTime:  lrm.getLastRotationTime(backupFiles),
		RotationCount:     int64(len(backupFiles)),
		IsRotationHealthy: isHealthy,
		LastUpdate:        time.Now(),
	}

	lrm.logger.Debug("Log rotation data collected",
		zap.Int64("current_size_mb", lrm.data.CurrentLogSizeMB),
		zap.Int("backup_count", lrm.data.BackupCount),
		zap.Float64("disk_usage_percent", lrm.data.DiskUsagePercent),
		zap.Bool("is_healthy", lrm.data.IsRotationHealthy),
	)
}

// getCurrentLogInfo 获取当前日志文件信息
func (lrm *LogRotationMonitor) getCurrentLogInfo() LogFileInfo {
	info := LogFileInfo{
		FileName: filepath.Base(lrm.logPath),
	}

	if stat, err := os.Stat(lrm.logPath); err == nil {
		info.SizeMB = stat.Size() / 1024 / 1024
		info.ModTime = stat.ModTime()
	} else {
		lrm.logger.Warn("Failed to get current log file info", zap.Error(err))
	}

	return info
}

// getBackupFiles 获取备份文件信息
func (lrm *LogRotationMonitor) getBackupFiles() []LogFileInfo {
	var backupFiles []LogFileInfo

	logDir := filepath.Dir(lrm.logPath)
	logBaseName := filepath.Base(lrm.logPath)

	// 读取日志目录
	entries, err := os.ReadDir(logDir)
	if err != nil {
		lrm.logger.Warn("Failed to read log directory", zap.Error(err))
		return backupFiles
	}

	// 查找备份文件
	for _, entry := range entries {
		fileName := entry.Name()

		// 检查是否是备份文件（以日志文件名开头且包含数字或.gz）
		if strings.HasPrefix(fileName, logBaseName) && fileName != logBaseName {
			if strings.Contains(fileName, ".") && (strings.Contains(fileName, ".gz") ||
				strings.Contains(fileName, ".1") || strings.Contains(fileName, ".2")) {

				filePath := filepath.Join(logDir, fileName)
				if stat, err := os.Stat(filePath); err == nil {
					backupFiles = append(backupFiles, LogFileInfo{
						FileName:     fileName,
						SizeMB:       stat.Size() / 1024 / 1024,
						ModTime:      stat.ModTime(),
						IsCompressed: strings.HasSuffix(fileName, ".gz"),
					})
				}
			}
		}
	}

	return backupFiles
}

// getDiskInfo 获取磁盘信息
func (lrm *LogRotationMonitor) getDiskInfo() struct {
	TotalMB      int64
	UsedMB       int64
	AvailableMB  int64
	UsagePercent float64
} {
	result := struct {
		TotalMB      int64
		UsedMB       int64
		AvailableMB  int64
		UsagePercent float64
	}{}

	logDir := filepath.Dir(lrm.logPath)

	var stat syscall.Statfs_t
	if err := syscall.Statfs(logDir, &stat); err != nil {
		lrm.logger.Warn("Failed to get disk info", zap.Error(err))
		return result
	}

	// 计算磁盘空间（转换为MB）
	blockSize := int64(stat.Bsize)
	totalBlocks := int64(stat.Blocks)
	freeBlocks := int64(stat.Bavail)

	result.TotalMB = (totalBlocks * blockSize) / 1024 / 1024
	result.AvailableMB = (freeBlocks * blockSize) / 1024 / 1024
	result.UsedMB = result.TotalMB - result.AvailableMB

	if result.TotalMB > 0 {
		result.UsagePercent = float64(result.UsedMB) / float64(result.TotalMB) * 100
	}

	return result
}

// checkRotationHealth 检查轮转健康状态
func (lrm *LogRotationMonitor) checkRotationHealth(currentLog LogFileInfo, backupFiles []LogFileInfo) bool {
	// 检查当前日志文件大小是否过大（超过200MB认为可能轮转有问题）
	if currentLog.SizeMB > 200 {
		return false
	}

	// 检查是否有备份文件（如果运行时间较长但没有备份文件可能有问题）
	if len(backupFiles) == 0 && currentLog.SizeMB > 50 {
		return false
	}

	// 检查最近是否有轮转（如果当前文件很大但最近没有轮转）
	lastRotation := lrm.getLastRotationTime(backupFiles)
	if !lastRotation.IsZero() && time.Since(lastRotation) > 24*time.Hour && currentLog.SizeMB > 100 {
		return false
	}

	return true
}

// getLastRotationTime 获取最后轮转时间
func (lrm *LogRotationMonitor) getLastRotationTime(backupFiles []LogFileInfo) time.Time {
	var lastTime time.Time

	for _, file := range backupFiles {
		if file.ModTime.After(lastTime) {
			lastTime = file.ModTime
		}
	}

	return lastTime
}

// GetCurrentLogSize 获取当前日志文件大小
func (lrm *LogRotationMonitor) GetCurrentLogSize() int64 {
	data := lrm.GetLogRotationData()
	return data.CurrentLogSizeMB
}

// GetDiskUsage 获取磁盘使用率
func (lrm *LogRotationMonitor) GetDiskUsage() float64 {
	data := lrm.GetLogRotationData()
	return data.DiskUsagePercent
}

// IsRotationHealthy 检查轮转是否健康
func (lrm *LogRotationMonitor) IsRotationHealthy() bool {
	data := lrm.GetLogRotationData()
	return data.IsRotationHealthy
}

// IsDiskSpaceLow 检查磁盘空间是否不足
func (lrm *LogRotationMonitor) IsDiskSpaceLow() bool {
	data := lrm.GetLogRotationData()
	return data.DiskUsagePercent > 90 // 磁盘使用率超过90%认为空间不足
}

// GetLogSummary 获取日志摘要信息
func (lrm *LogRotationMonitor) GetLogSummary() map[string]interface{} {
	data := lrm.GetLogRotationData()
	return map[string]interface{}{
		"current_size_mb":    data.CurrentLogSizeMB,
		"backup_count":       data.BackupCount,
		"total_backup_mb":    data.TotalBackupSizeMB,
		"disk_usage_percent": data.DiskUsagePercent,
		"is_healthy":         data.IsRotationHealthy,
		"last_rotation":      data.LastRotationTime,
	}
}
