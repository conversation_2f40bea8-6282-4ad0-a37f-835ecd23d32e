# AdIndex自动清理功能

## 功能概述

新增了AdIndex文件的自动清理功能，可以自动清理本地和远程存储中的旧文件，每种类型只保留最近的N个文件（默认3个）。

## 配置说明

在配置文件 `conf/rtb_ms_exporter.yaml` 中的 `adindex` 部分新增了 `keep_files` 配置项：

```yaml
adindex:
  watch_dir: /path/to/watch/directory
  file_pattern: '^ad_\w+\.data\.\d{8}_\d{6}$'
  keep_files: 3  # 每种类型保留的文件数量
```

## 功能特性

### 1. 自动触发
- 当所有必需的AdIndex文件类型都完成上传并创建VERSION文件后，自动触发清理功能
- 无需手动干预，完全自动化

### 2. 本地文件清理
- 扫描监听目录下的所有AdIndex文件
- 按文件类型分组（ad_campaign, ad_creative, ad_poi等）
- 按版本号排序，保留最新的N个文件
- 删除多余的旧文件

### 3. 远程文件清理
- 遍历所有配置的对象存储提供商（BOS、COS等）
- 列出adindex目录下的所有文件
- 按文件类型分组
- 按最后修改时间排序，保留最新的N个文件
- 删除多余的旧文件
- 不会删除VERSION文件

### 4. 支持的文件类型
- ad_campaign
- ad_creative
- ad_poi
- ad_promotion
- ad_sponsor
- ad_strategy
- ad_summary
- ad_tracking

## 日志记录

清理过程会详细记录日志，包括：
- 清理开始和结束
- 每个删除的文件信息
- 错误信息（如果有）

示例日志：
```
INFO    Starting cleanup of old adindex files   {"keep_files": 3}
INFO    Removed old local file  {"file": "ad_campaign.data.20240101_120000", "type": "ad_campaign", "version": "20240101_120000"}
INFO    Removed old remote file {"provider": "bos", "file": "adindex/ad_campaign.data.20240101_120000", "type": "ad_campaign"}
```

## 配置建议

- **生产环境**：建议设置 `keep_files: 3-5`，保留足够的历史版本用于回滚
- **测试环境**：可以设置 `keep_files: 2`，节省存储空间
- **开发环境**：可以设置 `keep_files: 1`，只保留最新版本

## 注意事项

1. **文件命名格式**：清理功能依赖于标准的文件命名格式 `ad_TYPE.data.YYYYMMDD_HHIISS`
2. **版本比较**：本地文件按版本号字符串比较，远程文件按最后修改时间比较
3. **错误处理**：如果清理过程中出现错误，会记录日志但不会中断程序运行
4. **并发安全**：清理操作在VERSION文件创建后执行，确保不会影响正在进行的文件上传

## 测试方法

1. 在监听目录下创建多个版本的测试文件：
```bash
touch ad_campaign.data.20240101_120000
touch ad_campaign.data.20240102_120000
touch ad_campaign.data.20240103_120000
touch ad_campaign.data.20240104_120000
touch ad_campaign.data.20240105_120000
```

2. 启动程序并观察日志，当所有文件类型完成后会自动清理

3. 检查目录，应该只保留最新的3个文件（如果keep_files设置为3）