# Monitor 监控模块

RTB MS Exporter 的监控模块，提供进程监控、错误日志监控和日志滚动监控功能。

## 功能概述

### 1. 进程监控 (health.go)
监控Go程序的运行状态和资源使用情况：
- **内存监控**：内存使用量、分配量、系统内存
- **并发监控**：Goroutine数量统计
- **GC监控**：垃圾回收次数、暂停时间、下次GC阈值
- **运行时监控**：程序启动时间、运行时长

### 2. 错误日志监控 (errors.go)
监控和统计业务错误：
- **错误统计**：按模块分类的错误计数（scheduler、storage、watcher等）
- **错误率计算**：每小时错误率统计
- **错误记录**：保留最近100个错误的详细信息
- **错误分类**：按错误类型和级别统计
- **实时监控**：通过zaplog钩子实时捕获错误

### 3. 日志滚动监控 (logrotation.go)
监控日志文件轮转状态：
- **文件大小监控**：当前日志文件大小和增长情况
- **轮转状态监控**：lumberjack轮转健康检查
- **备份文件监控**：备份文件数量、大小、压缩状态
- **磁盘空间监控**：磁盘使用率、剩余空间、空间预警

## 文件结构

```
internal/monitor/
├── README.md           # 本文档
├── monitor.go          # 监控管理器，统一管理各监控组件
├── health.go           # 进程健康监控
├── errors.go           # 错误监控和统计
└── logrotation.go      # 日志轮转监控
```

## 配置

在 `conf/rtb_ms_exporter.yaml` 中配置监控：

```yaml
monitor:
  enabled: true         # 启用监控
  report_interval: "1h" # 监控报告间隔（支持：30s, 5m, 1h等）
```

## 监控数据输出

### 输出位置
监控数据输出到主日志文件：`log/rtb_ms_exporter.log`

### 输出格式

#### 1. 定期监控报告
每小时（可配置）输出完整的监控摘要：
```json
{
  "level": "info",
  "time": "2025-08-05T14:17:25.043+0800",
  "msg": "Monitor report",
  "uptime": "1h30m45s",
  "memory_mb": 256.5,
  "goroutines": 15,
  "gc_count": 12,
  "total_errors": 3,
  "error_rate_per_hour": 2.0,
  "log_size_mb": 45,
  "log_backups": 2,
  "disk_usage_percent": 75.5,
  "rotation_healthy": true
}
```

#### 2. 错误统计详情
当有错误时，输出详细的错误统计：
```json
{
  "level": "info",
  "time": "2025-08-05T14:17:25.043+0800",
  "msg": "Module error stats",
  "module": "scheduler",
  "error_count": 2,
  "last_error": "2025-08-05T14:17:15.044+0800"
}
```

#### 3. 实时错误记录
错误发生时立即记录：
```json
{
  "level": "warn",
  "time": "2025-08-05T14:17:05.043+0800",
  "msg": "Error recorded",
  "module": "scheduler",
  "error_type": "task_failed",
  "message": "Budget task failed",
  "level": "error"
}
```

#### 4. 告警信息
磁盘使用率过高或日志轮转异常时的告警：
```json
{
  "level": "warn",
  "time": "2025-08-05T14:17:25.043+0800",
  "msg": "High disk usage detected",
  "usage_percent": 85.5,
  "available_mb": 1024
}
```

## 使用方法

### 1. 在代码中集成错误监控
```go
import "rtb_ms_exporter_go/internal/zaplog"

// 记录错误并触发监控
zaplog.LogError(logger, "scheduler", "task_failed", "Budget task failed", zap.Error(err))

// 记录警告并触发监控
zaplog.LogWarn(logger, "storage", "upload_slow", "Upload is slow", zap.Duration("duration", 5*time.Second))
```

### 2. 查看监控信息

#### 查看实时监控报告
```bash
tail -f log/rtb_ms_exporter.log | grep "Monitor report"
```

#### 查看错误统计
```bash
grep "Error recorded\|Module error stats" log/rtb_ms_exporter.log
```

#### 查看告警信息
```bash
grep "High disk usage\|Log rotation unhealthy" log/rtb_ms_exporter.log
```

#### 查看所有监控相关日志
```bash
grep "Monitor\|Error recorded\|High disk usage\|Log rotation unhealthy" log/rtb_ms_exporter.log
```

## 监控指标说明

| 指标 | 说明 | 正常范围 |
|------|------|----------|
| memory_mb | 内存使用量(MB) | < 1000MB |
| goroutines | Goroutine数量 | < 1000 |
| total_errors | 总错误数 | 越少越好 |
| error_rate_per_hour | 每小时错误率 | < 10 |
| disk_usage_percent | 磁盘使用率(%) | < 80% |
| rotation_healthy | 日志轮转健康状态 | true |

## 注意事项

1. **性能影响**：监控组件设计为轻量级，对系统性能影响极小
2. **日志轮转**：监控数据输出到主日志文件，会随日志文件一起轮转
3. **错误阈值**：可根据实际情况调整告警阈值（在代码中修改）
4. **存储空间**：监控数据占用的存储空间很小，主要是文本日志
5. **Linux兼容**：所有监控功能都兼容Linux生产环境

## 扩展说明

如需添加新的监控指标或修改告警阈值，可以：
1. 在对应的监控组件中添加新的数据收集逻辑
2. 在 `monitor.go` 的 `generateReport()` 方法中添加新的输出字段
3. 根据需要调整告警条件和阈值
