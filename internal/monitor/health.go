package monitor

import (
	"runtime"
	"sync"
	"time"

	"go.uber.org/zap"
)

// HealthMonitor 进程健康监控器
type HealthMonitor struct {
	logger    *zap.Logger
	startTime time.Time
	mu        sync.RWMutex
	data      HealthData
	stopCh    chan struct{}
}

// HealthData 健康监控数据
type HealthData struct {
	// 基本信息
	StartTime    time.Time `json:"start_time"`
	Uptime       string    `json:"uptime"`
	
	// 内存信息
	MemoryUsageMB    float64 `json:"memory_usage_mb"`
	MemoryAllocMB    float64 `json:"memory_alloc_mb"`
	MemoryTotalMB    float64 `json:"memory_total_mb"`
	MemorySystemMB   float64 `json:"memory_system_mb"`
	
	// Goroutine信息
	GoroutineCount   int `json:"goroutine_count"`
	
	// GC信息
	GCCount          uint32  `json:"gc_count"`
	GCPauseMs        float64 `json:"gc_pause_ms"`
	NextGCMB         float64 `json:"next_gc_mb"`
	
	// CPU信息
	CPUCount         int `json:"cpu_count"`
	
	// 更新时间
	LastUpdate       time.Time `json:"last_update"`
}

// NewHealthMonitor 创建健康监控器
func NewHealthMonitor(logger *zap.Logger) *HealthMonitor {
	return &HealthMonitor{
		logger:    logger,
		startTime: time.Now(),
		stopCh:    make(chan struct{}),
	}
}

// Start 启动健康监控
func (hm *HealthMonitor) Start() error {
	hm.logger.Info("Starting health monitor")
	
	// 立即收集一次数据
	hm.collectHealthData()
	
	// 启动定期收集
	go hm.collectLoop()
	
	return nil
}

// Stop 停止健康监控
func (hm *HealthMonitor) Stop() {
	close(hm.stopCh)
	hm.logger.Info("Health monitor stopped")
}

// GetHealthData 获取健康数据
func (hm *HealthMonitor) GetHealthData() HealthData {
	hm.mu.RLock()
	defer hm.mu.RUnlock()
	return hm.data
}

// collectLoop 数据收集循环
func (hm *HealthMonitor) collectLoop() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒收集一次
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			hm.collectHealthData()
		case <-hm.stopCh:
			return
		}
	}
}

// collectHealthData 收集健康数据
func (hm *HealthMonitor) collectHealthData() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	// 强制GC以获取更准确的内存信息
	runtime.GC()
	runtime.ReadMemStats(&m)
	
	hm.mu.Lock()
	defer hm.mu.Unlock()
	
	hm.data = HealthData{
		StartTime:        hm.startTime,
		Uptime:          time.Since(hm.startTime).String(),
		MemoryUsageMB:   bytesToMB(m.Alloc),
		MemoryAllocMB:   bytesToMB(m.TotalAlloc),
		MemoryTotalMB:   bytesToMB(m.Sys),
		MemorySystemMB:  bytesToMB(m.Sys),
		GoroutineCount:  runtime.NumGoroutine(),
		GCCount:         m.NumGC,
		GCPauseMs:       float64(m.PauseNs[(m.NumGC+255)%256]) / 1000000, // 最近一次GC暂停时间(ms)
		NextGCMB:        bytesToMB(m.NextGC),
		CPUCount:        runtime.NumCPU(),
		LastUpdate:      time.Now(),
	}
	
	// 记录详细的健康信息到日志
	hm.logger.Debug("Health data collected",
		zap.Float64("memory_mb", hm.data.MemoryUsageMB),
		zap.Int("goroutines", hm.data.GoroutineCount),
		zap.Uint32("gc_count", hm.data.GCCount),
		zap.Float64("gc_pause_ms", hm.data.GCPauseMs),
	)
}

// GetMemoryInfo 获取内存信息摘要
func (hm *HealthMonitor) GetMemoryInfo() map[string]interface{} {
	data := hm.GetHealthData()
	return map[string]interface{}{
		"usage_mb":  data.MemoryUsageMB,
		"alloc_mb":  data.MemoryAllocMB,
		"system_mb": data.MemorySystemMB,
		"next_gc_mb": data.NextGCMB,
	}
}

// GetGoroutineInfo 获取Goroutine信息
func (hm *HealthMonitor) GetGoroutineInfo() map[string]interface{} {
	data := hm.GetHealthData()
	return map[string]interface{}{
		"count": data.GoroutineCount,
		"cpu_count": data.CPUCount,
	}
}

// GetGCInfo 获取GC信息
func (hm *HealthMonitor) GetGCInfo() map[string]interface{} {
	data := hm.GetHealthData()
	return map[string]interface{}{
		"count": data.GCCount,
		"pause_ms": data.GCPauseMs,
		"next_gc_mb": data.NextGCMB,
	}
}

// IsHealthy 检查是否健康
func (hm *HealthMonitor) IsHealthy() bool {
	data := hm.GetHealthData()
	
	// 简单的健康检查规则
	if data.MemoryUsageMB > 1000 { // 内存使用超过1GB
		return false
	}
	
	if data.GoroutineCount > 1000 { // Goroutine数量超过1000
		return false
	}
	
	// 检查是否长时间没有更新数据
	if time.Since(data.LastUpdate) > 2*time.Minute {
		return false
	}
	
	return true
}

// GetHealthStatus 获取健康状态
func (hm *HealthMonitor) GetHealthStatus() string {
	if hm.IsHealthy() {
		return "healthy"
	}
	return "unhealthy"
}

// GetUptimeSeconds 获取运行时间（秒）
func (hm *HealthMonitor) GetUptimeSeconds() int64 {
	return int64(time.Since(hm.startTime).Seconds())
}

// bytesToMB 字节转换为MB
func bytesToMB(bytes uint64) float64 {
	return float64(bytes) / 1024 / 1024
}

// ForceGC 强制执行垃圾回收
func (hm *HealthMonitor) ForceGC() {
	hm.logger.Info("Forcing garbage collection")
	runtime.GC()
	hm.collectHealthData()
	hm.logger.Info("Garbage collection completed")
}
