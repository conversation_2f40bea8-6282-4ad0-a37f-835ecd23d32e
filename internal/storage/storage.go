package storage

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"rtb_ms_exporter_go/conf"
	"strings"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/bos/api"
	"github.com/tencentyun/cos-go-sdk-v5"
	"go.uber.org/zap"
)

// StorageProvider 对象存储提供商接口
type StorageProvider interface {
	// UploadFile 上传文件
	UploadFile(key string, content io.Reader, contentType string) error
	// DeleteFile 删除文件
	DeleteFile(key string) error
	// ListFiles 列出文件
	ListFiles(prefix string) ([]FileInfo, error)
	// CleanupOldFiles 清理旧文件
	CleanupOldFiles(prefix string, days int) error
	// Close 关闭连接
	Close() error
}

// FileInfo 文件信息
type FileInfo struct {
	Key          string
	Size         int64
	LastModified time.Time
}

// StorageManager 存储管理器
type StorageManager struct {
	providers map[string]StorageProvider
	logger    *zap.Logger
	debugMode bool // 调试模式，只打印不上传
}

// NewStorageManager 创建存储管理器
func NewStorageManager(logger *zap.Logger) *StorageManager {
	return &StorageManager{
		providers: make(map[string]StorageProvider),
		logger:    logger,
		debugMode: false,
	}
}

// SetDebugMode 设置调试模式
func (sm *StorageManager) SetDebugMode(debug bool) {
	sm.debugMode = debug
}

// InitProviders 初始化存储提供商
func (sm *StorageManager) InitProviders(configs map[string]conf.OSSConfig) error {
	for name, config := range configs {
		var provider StorageProvider
		var err error
		
		switch strings.ToLower(config.Type) {
		case "cos":
			provider, err = NewCOSProvider(config, sm.logger)
		case "bos":
			provider, err = NewBOSProvider(config, sm.logger)
		default:
			return fmt.Errorf("unsupported storage type: %s", config.Type)
		}
		
		if err != nil {
			return fmt.Errorf("failed to create %s provider: %v", config.Type, err)
		}
		
		sm.providers[name] = provider
		sm.logger.Info("Storage provider initialized", zap.String("name", name), zap.String("type", config.Type))
	}
	return nil
}

// GetProvider 获取存储提供商
func (sm *StorageManager) GetProvider(name string) (StorageProvider, error) {
	provider, exists := sm.providers[name]
	if !exists {
		return nil, fmt.Errorf("storage provider %s not found", name)
	}
	return provider, nil
}

// GetAllProviders 获取所有存储提供商
func (sm *StorageManager) GetAllProviders() map[string]StorageProvider {
	return sm.providers
}

// GetProviders 获取所有存储提供商（别名方法）
func (sm *StorageManager) GetProviders() map[string]StorageProvider {
	return sm.providers
}

// UploadToAll 上传到所有提供商
func (sm *StorageManager) UploadToAll(key string, content io.Reader, contentType string) error {
	contentBytes, err := io.ReadAll(content)
	if err != nil {
		return fmt.Errorf("failed to read content: %v", err)
	}
	
	// 调试模式：只打印上传信息，不实际上传
	if sm.debugMode {
		sm.logger.Info("[DEBUG MODE] Would upload file", 
			zap.String("key", key), 
			zap.String("contentType", contentType),
			zap.Int("contentSize", len(contentBytes)))
		for name := range sm.providers {
			sm.logger.Info("[DEBUG MODE] Would upload to provider", 
				zap.String("provider", name), 
				zap.String("key", key))
		}
		return nil
	}
	
	for name, provider := range sm.providers {
		startTime := time.Now()
		contentReader := strings.NewReader(string(contentBytes))
		
		sm.logger.Info("Starting upload to provider", 
			zap.String("provider", name), 
			zap.String("key", key),
			zap.Int("size", len(contentBytes)))
		
		if err := provider.UploadFile(key, contentReader, contentType); err != nil {
			duration := time.Since(startTime)
			sm.logger.Error("Failed to upload to provider", 
				zap.String("provider", name), 
				zap.String("key", key), 
				zap.Duration("duration", duration),
				zap.Error(err))
			return err
		}
		
		duration := time.Since(startTime)
		sm.logger.Info("File uploaded to provider", 
			zap.String("provider", name), 
			zap.String("key", key),
			zap.Int("size", len(contentBytes)),
			zap.Duration("duration", duration))
	}
	return nil
}

// CleanupAllProviders 清理所有提供商的旧文件
func (sm *StorageManager) CleanupAllProviders(prefix string, days int) error {
	for name, provider := range sm.providers {
		if err := provider.CleanupOldFiles(prefix, days); err != nil {
			sm.logger.Error("Failed to cleanup provider", zap.String("provider", name), zap.Error(err))
			return err
		}
		sm.logger.Info("Provider cleaned up", zap.String("provider", name))
	}
	return nil
}

// Close 关闭所有提供商
func (sm *StorageManager) Close() {
	for name, provider := range sm.providers {
		if err := provider.Close(); err != nil {
			sm.logger.Error("Failed to close provider", zap.String("provider", name), zap.Error(err))
		} else {
			sm.logger.Info("Provider closed", zap.String("provider", name))
		}
	}
}

// COSProvider 腾讯云COS提供商
type COSProvider struct {
	client *cos.Client
	logger *zap.Logger
}

// NewCOSProvider 创建COS提供商
func NewCOSProvider(config conf.OSSConfig, logger *zap.Logger) (*COSProvider, error) {
	u, err := url.Parse(config.Endpoint)
	if err != nil {
		return nil, err
	}
	
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Timeout: 100 * time.Second,
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.AccessKeyID,
			SecretKey: config.AccessKeySecret,
		},
	})
	
	return &COSProvider{
		client: client,
		logger: logger,
	}, nil
}

// UploadFile 上传文件到COS
func (p *COSProvider) UploadFile(key string, content io.Reader, contentType string) error {
	_, err := p.client.Object.Put(context.Background(), key, content, &cos.ObjectPutOptions{
		ObjectPutHeaderOptions: &cos.ObjectPutHeaderOptions{
			ContentType: contentType,
		},
	})
	return err
}

// DeleteFile 从COS删除文件
func (p *COSProvider) DeleteFile(key string) error {
	_, err := p.client.Object.Delete(context.Background(), key)
	return err
}

// ListFiles 列出COS文件
func (p *COSProvider) ListFiles(prefix string) ([]FileInfo, error) {
	result, _, err := p.client.Bucket.Get(context.Background(), &cos.BucketGetOptions{
		Prefix: prefix,
	})
	if err != nil {
		return nil, err
	}
	
	var files []FileInfo
	for _, obj := range result.Contents {
		lastModified, _ := time.Parse(time.RFC3339, obj.LastModified)
		files = append(files, FileInfo{
			Key:          obj.Key,
			Size:         obj.Size,
			LastModified: lastModified,
		})
	}
	return files, nil
}

// CleanupOldFiles 清理COS旧文件
func (p *COSProvider) CleanupOldFiles(prefix string, days int) error {
	files, err := p.ListFiles(prefix)
	if err != nil {
		return err
	}
	
	cutoff := time.Now().AddDate(0, 0, -days)
	for _, file := range files {
		if file.LastModified.Before(cutoff) {
			if err := p.DeleteFile(file.Key); err != nil {
				p.logger.Error("Failed to delete old file", zap.String("key", file.Key), zap.Error(err))
				return err
			}
			p.logger.Info("Deleted old file", zap.String("key", file.Key))
		}
	}
	return nil
}

// Close 关闭COS连接
func (p *COSProvider) Close() error {
	return nil
}

// BOSProvider 百度云BOS提供商
type BOSProvider struct {
	client     *bos.Client
	bucketName string
	logger     *zap.Logger
}

// NewBOSProvider 创建BOS提供商
func NewBOSProvider(config conf.OSSConfig, logger *zap.Logger) (*BOSProvider, error) {
	client, err := bos.NewClient(config.AccessKeyID, config.AccessKeySecret, config.Endpoint)
	if err != nil {
		return nil, err
	}
	
	return &BOSProvider{
		client:     client,
		bucketName: config.BucketName,
		logger:     logger,
	}, nil
}

// UploadFile 上传文件到BOS
func (p *BOSProvider) UploadFile(key string, content io.Reader, contentType string) error {
	// 读取内容以获取大小
	contentBytes, err := io.ReadAll(content)
	if err != nil {
		return err
	}
	
	body, err := bce.NewBodyFromBytes(contentBytes)
	if err != nil {
		return err
	}
	
	_, err = p.client.PutObject(p.bucketName, key, body, nil)
	return err
}

// DeleteFile 从BOS删除文件
func (p *BOSProvider) DeleteFile(key string) error {
	return p.client.DeleteObject(p.bucketName, key)
}

// ListFiles 列出BOS文件
func (p *BOSProvider) ListFiles(prefix string) ([]FileInfo, error) {
	result, err := p.client.ListObjects(p.bucketName, &api.ListObjectsArgs{
		Prefix: prefix,
	})
	if err != nil {
		return nil, err
	}
	
	var files []FileInfo
	for _, obj := range result.Contents {
		lastModified, _ := time.Parse(time.RFC3339, obj.LastModified)
		files = append(files, FileInfo{
			Key:          obj.Key,
			Size:         int64(obj.Size),
			LastModified: lastModified,
		})
	}
	return files, nil
}

// CleanupOldFiles 清理BOS旧文件
func (p *BOSProvider) CleanupOldFiles(prefix string, days int) error {
	files, err := p.ListFiles(prefix)
	if err != nil {
		return err
	}
	
	cutoff := time.Now().AddDate(0, 0, -days)
	for _, file := range files {
		if file.LastModified.Before(cutoff) {
			if err := p.DeleteFile(file.Key); err != nil {
				p.logger.Error("Failed to delete old file", zap.String("key", file.Key), zap.Error(err))
				return err
			}
			p.logger.Info("Deleted old file", zap.String("key", file.Key))
		}
	}
	return nil
}

// Close 关闭BOS连接
func (p *BOSProvider) Close() error {
	return nil
}