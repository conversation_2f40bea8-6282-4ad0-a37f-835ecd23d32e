# RTB MS Exporter Makefile

# 变量定义
APP_NAME = rtb_ms_exporter
VERSION = $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME = $(shell date +"%Y-%m-%d %H:%M:%S")
GO_VERSION = $(shell go version | awk '{print $$3}')
LDFLAGS = -ldflags "-X main.__version__=$(VERSION) -X 'main.__build_time__=$(BUILD_TIME)' -X 'main.__go_version__=$(GO_VERSION)'"

# 默认目标
.PHONY: all
all: build

# 构建
.PHONY: build
build:
	@echo "Building $(APP_NAME)..."
	go build $(LDFLAGS) -o $(APP_NAME) cmd/main.go
	@echo "Build completed: $(APP_NAME)"

# 清理
.PHONY: clean
clean:
	@echo "Cleaning..."
	rm -f $(APP_NAME)
	@echo "Clean completed"

# 运行
.PHONY: run
run: build
	@echo "Running $(APP_NAME)..."
	./$(APP_NAME) -c conf/rtb_ms_exporter.yaml

# 测试
.PHONY: test
test:
	@echo "Running tests..."
	go test -v ./...

# 格式化代码
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	go fmt ./...

# 检查代码
.PHONY: vet
vet:
	@echo "Vetting code..."
	go vet ./...

# 下载依赖
.PHONY: deps
deps:
	@echo "Downloading dependencies..."
	go mod download
	go mod tidy

# 安装
.PHONY: install
install: build
	@echo "Installing $(APP_NAME)..."
	cp $(APP_NAME) /usr/local/bin/
	@echo "Installation completed"

# 创建发布包
.PHONY: package
package: build
	@echo "Creating package..."
	mkdir -p dist
	tar -czf dist/$(APP_NAME)-$(VERSION).tar.gz $(APP_NAME) conf/ README.md
	@echo "Package created: dist/$(APP_NAME)-$(VERSION).tar.gz"

# 显示版本信息
.PHONY: version
version:
	@echo "Version: $(VERSION)"
	@echo "Build Time: $(BUILD_TIME)"
	@echo "Go Version: $(GO_VERSION)"

# 显示帮助
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  build    - Build the application"
	@echo "  clean    - Clean build artifacts"
	@echo "  run      - Build and run the application"
	@echo "  test     - Run tests"
	@echo "  fmt      - Format code"
	@echo "  vet      - Vet code"
	@echo "  deps     - Download and tidy dependencies"
	@echo "  install  - Install to /usr/local/bin"
	@echo "  package  - Create release package"
	@echo "  version  - Show version information"
	@echo "  help     - Show this help"