package models

import (
	"database/sql"
	"fmt"
	"rtb_ms_exporter_go/conf"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"go.uber.org/zap"
)

// BudgetStats budget统计数据结构
type BudgetStats struct {
	UID        int64   `orm:"column(uid)"`
	PID        int64   `orm:"column(pid)"`
	SID        int64   `orm:"column(sid)"`
	CID        int64   `orm:"column(cid)"`
	Bid        int64   `orm:"column(bid)"`
	Win        int64   `orm:"column(win)"`
	Imp        int64   `orm:"column(imp)"`
	Clk        int64   `orm:"column(clk)"`
	Act        int64   `orm:"column(act)"`
	MediaPrice float64 `orm:"column(media_price)"`
	Price      float64 `orm:"column(price)"`
}

// DeviceMapping 设备映射数据结构
type DeviceMapping struct {
	BrandName     string `orm:"column(brand_name)"`
	BrandID       int64  `orm:"column(brand_id)"`
	InternalModel string `orm:"column(internal_model)"`
	DisplayModel  string `orm:"column(display_model)"`
	ModelID       int64  `orm:"column(model_id)"`
}

// ResourceTarget 资源目标数据结构
type ResourceTarget struct {
	ResourceID     int64  `orm:"column(resource_id)"`
	AdPosID        string `orm:"column(ad_pos_id)"`
	Type           string `orm:"column(type)"`
	ExchangeIDs    string `orm:"column(exchange_ids)"`
	AdxExchangeIDs string `orm:"column(adx_exchange_ids)"`
	MediaIDs       string `orm:"column(media_ids)"`
}

// DatabaseManager 数据库管理器
type DatabaseManager struct {
	dbs    map[string]*sql.DB
	logger *zap.Logger
}

// NewDatabaseManager 创建数据库管理器
func NewDatabaseManager(logger *zap.Logger) *DatabaseManager {
	return &DatabaseManager{
		dbs:    make(map[string]*sql.DB),
		logger: logger,
	}
}

// InitDatabases 初始化数据库连接
func (dm *DatabaseManager) InitDatabases(configs map[string]conf.DatabaseConfig) error {
	for name, config := range configs {
		// 添加连接参数来处理prepared statement问题
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=true&loc=Local&interpolateParams=true",
			config.User, config.Password, config.Host, config.Port, config.DBName, config.Charset)

		db, err := sql.Open("mysql", dsn)
		if err != nil {
			return fmt.Errorf("failed to open database %s: %v", name, err)
		}

		// 测试连接
		if err := db.Ping(); err != nil {
			return fmt.Errorf("failed to ping database %s: %v", name, err)
		}

		// 设置连接池参数，减少连接生命周期以避免prepared statement问题
		db.SetMaxOpenConns(10)
		db.SetMaxIdleConns(5)
		db.SetConnMaxLifetime(30 * time.Minute) // 减少连接生命周期
		db.SetConnMaxIdleTime(10 * time.Minute) // 设置空闲连接超时

		dm.dbs[name] = db
		dm.logger.Info("Database connected", zap.String("name", name))
	}
	return nil
}

// GetDB 获取数据库连接
func (dm *DatabaseManager) GetDB(name string) (*sql.DB, error) {
	db, exists := dm.dbs[name]
	if !exists {
		return nil, fmt.Errorf("database %s not found", name)
	}
	return db, nil
}

// QueryBudgetStats 查询budget统计数据
func (dm *DatabaseManager) QueryBudgetStats(dt string) ([]BudgetStats, error) {
	return dm.queryBudgetStatsWithRetry(dt, 3)
}

// queryBudgetStatsWithRetry 带重试的查询budget统计数据
func (dm *DatabaseManager) queryBudgetStatsWithRetry(dt string, maxRetries int) ([]BudgetStats, error) {
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			dm.logger.Warn("Retrying budget stats query", zap.Int("attempt", attempt+1))
			time.Sleep(time.Duration(attempt) * time.Second)
		}

		db, err := dm.GetDB("rtb")
		if err != nil {
			lastErr = err
			continue
		}

		// 测试连接是否有效
		if err := db.Ping(); err != nil {
			dm.logger.Warn("Database ping failed, will retry", zap.Error(err))
			lastErr = err
			continue
		}

		query := `
			SELECT 
				uid, pid, sid, cid,
				sum(bid) as bid,
				sum(win) as win,
				sum(imp) as imp,
				sum(clk) as clk,
				sum(act) as act,
				sum(media_price) as media_price,
				sum(settled_ad_price) as price
			FROM rtb_stats_daily_view 
			WHERE dt = ? and cid > 0 and cid < 100000000
			GROUP BY uid, pid, sid, cid
		`
		rows, err := db.Query(query, dt)
		if err != nil {
			// 检查是否是prepared statement错误
			if strings.Contains(err.Error(), "Prepared statement needs to be re-prepared") {
				dm.logger.Warn("Prepared statement error detected, will retry", zap.Error(err))
				lastErr = err
				continue
			}
			lastErr = err
			continue
		}
		defer rows.Close()

		var results []BudgetStats
		for rows.Next() {
			var stat BudgetStats
			err := rows.Scan(&stat.UID, &stat.PID, &stat.SID, &stat.CID,
				&stat.Bid, &stat.Win, &stat.Imp, &stat.Clk, &stat.Act,
				&stat.MediaPrice, &stat.Price)
			if err != nil {
				lastErr = err
				break
			}
			results = append(results, stat)
		}

		if lastErr == nil {
			return results, nil
		}
	}

	return nil, fmt.Errorf("failed to query budget stats after %d attempts: %v", maxRetries, lastErr)
}

// QueryDeviceMapping 查询设备映射数据
func (dm *DatabaseManager) QueryDeviceMapping() ([]DeviceMapping, error) {
	return dm.queryDeviceMappingWithRetry(3)
}

// queryDeviceMappingWithRetry 带重试的查询设备映射数据
func (dm *DatabaseManager) queryDeviceMappingWithRetry(maxRetries int) ([]DeviceMapping, error) {
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			dm.logger.Warn("Retrying device mapping query", zap.Int("attempt", attempt+1))
			time.Sleep(time.Duration(attempt) * time.Second)
		}

		db, err := dm.GetDB("device")
		if err != nil {
			lastErr = err
			continue
		}

		// 测试连接是否有效
		if err := db.Ping(); err != nil {
			dm.logger.Warn("Database ping failed, will retry", zap.Error(err))
			lastErr = err
			continue
		}

		query := `
			SELECT 
				dm.brand as brand_name,
				dv_brand.brandId as brand_id,
				dm.internal_model,
				dv_brand.name as display_model,
				dm.model_id
			FROM device_model dm
			JOIN device_v2 dv_brand on dm.model_id=dv_brand.id
				AND dv_brand.type = 'model'
			WHERE dm.status = 1
			ORDER BY brand_name, dm.model
		`

		rows, err := db.Query(query)
		if err != nil {
			// 检查是否是prepared statement错误
			if strings.Contains(err.Error(), "Prepared statement needs to be re-prepared") {
				dm.logger.Warn("Prepared statement error detected, will retry", zap.Error(err))
				lastErr = err
				continue
			}
			lastErr = err
			continue
		}
		defer rows.Close()

		var results []DeviceMapping
		for rows.Next() {
			var mapping DeviceMapping
			err := rows.Scan(&mapping.BrandName, &mapping.BrandID,
				&mapping.InternalModel, &mapping.DisplayModel, &mapping.ModelID)
			if err != nil {
				lastErr = err
				break
			}
			results = append(results, mapping)
		}

		if lastErr == nil {
			return results, nil
		}
	}

	return nil, fmt.Errorf("failed to query device mapping after %d attempts: %v", maxRetries, lastErr)
}

// QueryResourceTarget 查询资源目标数据
func (dm *DatabaseManager) QueryResourceTarget() ([]ResourceTarget, error) {
	return dm.queryResourceTargetWithRetry(3)
}

// queryResourceTargetWithRetry 带重试的查询资源目标数据
func (dm *DatabaseManager) queryResourceTargetWithRetry(maxRetries int) ([]ResourceTarget, error) {
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			dm.logger.Warn("Retrying resource target query", zap.Int("attempt", attempt+1))
			time.Sleep(time.Duration(attempt) * time.Second)
		}

		db, err := dm.GetDB("bid")
		if err != nil {
			lastErr = err
			continue
		}

		// 测试连接是否有效
		if err := db.Ping(); err != nil {
			dm.logger.Warn("Database ping failed, will retry", zap.Error(err))
			lastErr = err
			continue
		}

		query := `
			SELECT 
				resource_id, ad_pos_id, type,
				exchange_ids, adx_exchange_ids, media_ids
			FROM resource_pos_medias
			WHERE status = 0
		`

		rows, err := db.Query(query)
		if err != nil {
			// 检查是否是prepared statement错误
			if strings.Contains(err.Error(), "Prepared statement needs to be re-prepared") {
				dm.logger.Warn("Prepared statement error detected, will retry", zap.Error(err))
				lastErr = err
				continue
			}
			lastErr = err
			continue
		}
		defer rows.Close()

		var results []ResourceTarget
		for rows.Next() {
			var target ResourceTarget
			err := rows.Scan(&target.ResourceID, &target.AdPosID, &target.Type,
				&target.ExchangeIDs, &target.AdxExchangeIDs, &target.MediaIDs)
			if err != nil {
				lastErr = err
				break
			}
			results = append(results, target)
		}

		if lastErr == nil {
			return results, nil
		}
	}

	return nil, fmt.Errorf("failed to query resource target after %d attempts: %v", maxRetries, lastErr)
}

// FormatBudgetStats 格式化budget统计数据为TSV格式
func FormatBudgetStats(stats []BudgetStats) string {
	if len(stats) == 0 {
		// 当没有数据时，返回表头注释
		return "# No budget stats data found for the specified date\n# Format: uid\tpid\tsid\tcid\tbid\twin\timp\tclk\tact\tmedia_price\tprice"
	}

	var lines []string
	for _, stat := range stats {
		line := fmt.Sprintf("%d\t%d\t%d\t%d\t%d\t%d\t%d\t%d\t%d\t%.2f\t%.2f",
			stat.UID, stat.PID, stat.SID, stat.CID, stat.Bid, stat.Win,
			stat.Imp, stat.Clk, stat.Act, stat.MediaPrice, stat.Price)
		lines = append(lines, line)
	}
	return strings.Join(lines, "\n")
}

// FormatDeviceMapping 格式化设备映射数据为TSV格式
func FormatDeviceMapping(mappings []DeviceMapping) string {
	var lines []string
	for _, mapping := range mappings {
		line := fmt.Sprintf("%s\t%d\t%s\t%s\t%d",
			mapping.BrandName, mapping.BrandID, mapping.InternalModel,
			mapping.DisplayModel, mapping.ModelID)
		lines = append(lines, line)
	}
	return strings.Join(lines, "\n")
}

// FormatResourceTarget 格式化资源目标数据为TSV格式
func FormatResourceTarget(targets []ResourceTarget) string {
	var lines []string
	for _, target := range targets {
		line := fmt.Sprintf("%d\t%s\t%s\t%s\t%s\t%s",
			target.ResourceID, target.AdPosID, target.Type,
			target.ExchangeIDs, target.AdxExchangeIDs, target.MediaIDs)
		lines = append(lines, line)
	}
	return strings.Join(lines, "\n")
}

// Close 关闭所有数据库连接
func (dm *DatabaseManager) Close() {
	for name, db := range dm.dbs {
		if err := db.Close(); err != nil {
			dm.logger.Error("Failed to close database", zap.String("name", name), zap.Error(err))
		} else {
			dm.logger.Info("Database closed", zap.String("name", name))
		}
	}
}
