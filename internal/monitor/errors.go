package monitor

import (
	"sync"
	"time"

	"go.uber.org/zap"
)

// ErrorMonitor 错误监控器
type ErrorMonitor struct {
	logger     *zap.Logger
	mu         sync.RWMutex
	data       ErrorData
	errorStats map[string]*ModuleErrorStats // 按模块统计错误
	recentErrors []ErrorRecord              // 最近的错误记录
	maxRecentErrors int                     // 最大保留的最近错误数量
	stopCh     chan struct{}
}

// ErrorData 错误监控数据
type ErrorData struct {
	TotalErrors    int64                        `json:"total_errors"`
	ErrorRate      float64                      `json:"error_rate"`      // 每小时错误率
	LastErrorTime  time.Time                    `json:"last_error_time"`
	ModuleStats    map[string]*ModuleErrorStats `json:"module_stats"`
	RecentErrors   []ErrorRecord                `json:"recent_errors"`
	LastUpdate     time.Time                    `json:"last_update"`
}

// ModuleErrorStats 模块错误统计
type ModuleErrorStats struct {
	ModuleName   string    `json:"module_name"`
	ErrorCount   int64     `json:"error_count"`
	LastError    time.Time `json:"last_error"`
	ErrorTypes   map[string]int64 `json:"error_types"` // 按错误类型统计
}

// ErrorRecord 错误记录
type ErrorRecord struct {
	Timestamp   time.Time `json:"timestamp"`
	Module      string    `json:"module"`
	ErrorType   string    `json:"error_type"`
	Message     string    `json:"message"`
	Level       string    `json:"level"`
}

// NewErrorMonitor 创建错误监控器
func NewErrorMonitor(logger *zap.Logger) *ErrorMonitor {
	return &ErrorMonitor{
		logger:          logger,
		errorStats:      make(map[string]*ModuleErrorStats),
		recentErrors:    make([]ErrorRecord, 0),
		maxRecentErrors: 100, // 保留最近100个错误
		stopCh:          make(chan struct{}),
		data: ErrorData{
			ModuleStats: make(map[string]*ModuleErrorStats),
		},
	}
}

// Start 启动错误监控
func (em *ErrorMonitor) Start() error {
	em.logger.Info("Starting error monitor")
	
	// 启动定期统计
	go em.statisticsLoop()
	
	return nil
}

// Stop 停止错误监控
func (em *ErrorMonitor) Stop() {
	close(em.stopCh)
	em.logger.Info("Error monitor stopped")
}

// RecordError 记录错误
func (em *ErrorMonitor) RecordError(module, errorType, message, level string) {
	em.mu.Lock()
	defer em.mu.Unlock()
	
	now := time.Now()
	
	// 更新总错误计数
	em.data.TotalErrors++
	em.data.LastErrorTime = now
	
	// 更新模块统计
	if em.errorStats[module] == nil {
		em.errorStats[module] = &ModuleErrorStats{
			ModuleName: module,
			ErrorTypes: make(map[string]int64),
		}
	}
	
	moduleStats := em.errorStats[module]
	moduleStats.ErrorCount++
	moduleStats.LastError = now
	moduleStats.ErrorTypes[errorType]++
	
	// 添加到最近错误记录
	errorRecord := ErrorRecord{
		Timestamp: now,
		Module:    module,
		ErrorType: errorType,
		Message:   message,
		Level:     level,
	}
	
	em.recentErrors = append(em.recentErrors, errorRecord)
	
	// 保持最近错误记录数量限制
	if len(em.recentErrors) > em.maxRecentErrors {
		em.recentErrors = em.recentErrors[1:]
	}
	
	// 记录到日志
	em.logger.Warn("Error recorded",
		zap.String("module", module),
		zap.String("error_type", errorType),
		zap.String("message", message),
		zap.String("level", level),
	)
}

// GetErrorData 获取错误数据
func (em *ErrorMonitor) GetErrorData() ErrorData {
	em.mu.RLock()
	defer em.mu.RUnlock()
	
	// 复制模块统计数据
	moduleStats := make(map[string]*ModuleErrorStats)
	for k, v := range em.errorStats {
		// 深拷贝错误类型统计
		errorTypes := make(map[string]int64)
		for et, count := range v.ErrorTypes {
			errorTypes[et] = count
		}
		
		moduleStats[k] = &ModuleErrorStats{
			ModuleName: v.ModuleName,
			ErrorCount: v.ErrorCount,
			LastError:  v.LastError,
			ErrorTypes: errorTypes,
		}
	}
	
	// 复制最近错误记录
	recentErrors := make([]ErrorRecord, len(em.recentErrors))
	copy(recentErrors, em.recentErrors)
	
	return ErrorData{
		TotalErrors:   em.data.TotalErrors,
		ErrorRate:     em.data.ErrorRate,
		LastErrorTime: em.data.LastErrorTime,
		ModuleStats:   moduleStats,
		RecentErrors:  recentErrors,
		LastUpdate:    time.Now(),
	}
}

// statisticsLoop 统计循环
func (em *ErrorMonitor) statisticsLoop() {
	ticker := time.NewTicker(1 * time.Minute) // 每分钟更新一次错误率
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			em.updateErrorRate()
		case <-em.stopCh:
			return
		}
	}
}

// updateErrorRate 更新错误率
func (em *ErrorMonitor) updateErrorRate() {
	em.mu.Lock()
	defer em.mu.Unlock()
	
	now := time.Now()
	oneHourAgo := now.Add(-1 * time.Hour)
	
	// 计算最近一小时的错误数量
	var recentErrorCount int64
	for _, errorRecord := range em.recentErrors {
		if errorRecord.Timestamp.After(oneHourAgo) {
			recentErrorCount++
		}
	}
	
	// 计算每小时错误率
	em.data.ErrorRate = float64(recentErrorCount)
	em.data.LastUpdate = now
	
	// 更新模块统计到数据中
	em.data.ModuleStats = make(map[string]*ModuleErrorStats)
	for k, v := range em.errorStats {
		errorTypes := make(map[string]int64)
		for et, count := range v.ErrorTypes {
			errorTypes[et] = count
		}
		
		em.data.ModuleStats[k] = &ModuleErrorStats{
			ModuleName: v.ModuleName,
			ErrorCount: v.ErrorCount,
			LastError:  v.LastError,
			ErrorTypes: errorTypes,
		}
	}
}

// GetModuleErrorCount 获取指定模块的错误数量
func (em *ErrorMonitor) GetModuleErrorCount(module string) int64 {
	em.mu.RLock()
	defer em.mu.RUnlock()
	
	if stats, exists := em.errorStats[module]; exists {
		return stats.ErrorCount
	}
	return 0
}

// GetErrorRate 获取错误率
func (em *ErrorMonitor) GetErrorRate() float64 {
	em.mu.RLock()
	defer em.mu.RUnlock()
	return em.data.ErrorRate
}

// GetTotalErrors 获取总错误数
func (em *ErrorMonitor) GetTotalErrors() int64 {
	em.mu.RLock()
	defer em.mu.RUnlock()
	return em.data.TotalErrors
}

// GetRecentErrors 获取最近的错误记录
func (em *ErrorMonitor) GetRecentErrors(limit int) []ErrorRecord {
	em.mu.RLock()
	defer em.mu.RUnlock()
	
	if limit <= 0 || limit > len(em.recentErrors) {
		limit = len(em.recentErrors)
	}
	
	// 返回最近的错误（倒序）
	result := make([]ErrorRecord, limit)
	for i := 0; i < limit; i++ {
		result[i] = em.recentErrors[len(em.recentErrors)-1-i]
	}
	
	return result
}

// ClearErrorStats 清除错误统计
func (em *ErrorMonitor) ClearErrorStats() {
	em.mu.Lock()
	defer em.mu.Unlock()
	
	em.data.TotalErrors = 0
	em.data.ErrorRate = 0
	em.errorStats = make(map[string]*ModuleErrorStats)
	em.recentErrors = make([]ErrorRecord, 0)
	em.data.ModuleStats = make(map[string]*ModuleErrorStats)
	
	em.logger.Info("Error statistics cleared")
}

// IsErrorRateHigh 检查错误率是否过高
func (em *ErrorMonitor) IsErrorRateHigh() bool {
	return em.GetErrorRate() > 10 // 每小时超过10个错误认为是高错误率
}
